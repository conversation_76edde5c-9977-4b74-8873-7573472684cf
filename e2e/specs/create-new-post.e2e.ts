import {
  setupE2ETestHooksWithPostManager,
  executeCommand,
  expectNotice,
  expectPostFile
} from '../helpers/shared-context';
import { completeModalInteraction } from '../helpers/modal-helpers';
import {
  openGhostTab,
  clickSyncButton,
  waitForGhostTabStatus,
  waitForSyncToComplete,
  getGhostTabSyncStatus
} from '../helpers/ghost-tab-helpers';

import { test, expect } from 'vitest';

describe("Commands: Create New Post", () => {
  const { context, postManager } = setupE2ETestHooksWithPostManager();

  test("should create a new post using Playwright Electron", async () => {
    const testTitle = "Test Post";

    await executeCommand(context, 'Ghost Sync: Create new post');

    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await expectPostFile(context, "test-post", { content: /Write your content here/ });
  });

  test("should create a new post and sync it to Ghost", async () => {
    const testTitle = "Test Post for Sync";

    await executeCommand(context, 'Ghost Sync: Create new post');

    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await expectPostFile(context, "test-post-for-sync", { content: /Write your content here/ });

    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    const syncStatus = await getGhostTabSyncStatus(context.page);
    expect(syncStatus.isNewPost).toBe(true);
    expect(syncStatus.title).toBe(testTitle);

    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "Synced");

    // Track the post that was created through UI interaction for cleanup
    if (postManager().isAvailable()) {
      await postManager().trackPostsCreatedDuringTest();
    }
  });


});
